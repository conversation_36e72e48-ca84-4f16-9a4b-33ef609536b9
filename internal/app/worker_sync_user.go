package app

import (
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
)

const agentUserQueueGroup = "AgentSyncUserConsumer"

func InitializeSyncUserWorker() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	initializer.InitNatsMeme()

	memeNats := global.GVA_NATS_MEME
	userDexStream := "dex_user"
	err := memeNats.EnsureReadonlyStreamExists(userDexStream)
	if err != nil {
		panic(fmt.Sprintf("Failed to ensure readonly stream %s exists: %v", userDexStream, err))
	}

	go runSyncUserNatsWorker()
	select {}
}

func runSyncUserNatsWorker() error {
	logger := global.GVA_LOG

	nc := global.GVA_NATS_MEME

	subjects := map[string]func(msg *nats.Msg) error{
		string(task.UserNewWalletSubject): task.ConsumeUserSyncInfoEvent,
	}

	subject := "dex.user.>"
	opts := []nats.SubOpt{
		nats.Durable(agentUserQueueGroup),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
	}
	_, err := nc.SubscribeQueue(subject, agentUserQueueGroup, func(msg *nats.Msg) {
		go func() {
			start := time.Now()
			defer func() {
				elapsed := time.Since(start)
				logger.Info("Worker process completed: %s duration=%s", zap.String("Subject", msg.Subject), zap.Duration("duration", elapsed))
			}()

			handler, ok := subjects[msg.Subject]
			if !ok || handler == nil {
				logger.Error("failed: Worker handler not found: %s", zap.String("Subject", msg.Subject))
				msg.Ack()
				return
			}

			err := handler(msg)
			if err != nil {
				logger.Error("Worker Consume subject %v failed: %v - %s", zap.String("Subject", msg.Subject), zap.Error(err), zap.Any("Data", msg.Data))
				msg.Nak()
			} else {
				msg.Ack()
			}
		}()
	}, opts...)
	if err != nil {
		logger.Error("Could not subscribe to subject %s: %v", zap.String("Subject", subject), zap.Error(err))
		return err
	}
	logger.Info("Worker started, listening on JetStream subjects.")
	return nil
}
