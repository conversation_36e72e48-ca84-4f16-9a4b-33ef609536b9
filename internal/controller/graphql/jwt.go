package graphql

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"go.uber.org/zap"
)

func GqlJwtAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Skip JWT validation for certain consumers if needed
		consumerName := ctx.GetHeader("X-Consumer-Username")
		if consumerName != "xbit" {
			ctx.Next()
			return
		}

		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			// No token provided, continue without setting user context
			// The @auth directive will handle the authorization check
			ctx.Next()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")

		// Validate JWT token using the proper validation function
		claims, err := utils.ValidateJWTToken(tokenString, global.GVA_CONFIG.JWT)
		if err != nil {
			// Log warning if logger is available
			if global.GVA_LOG != nil {
				global.GVA_LOG.Warn("Invalid JWT token",
					zap.Error(err),
					zap.String("token_prefix", tokenString[:min(len(tokenString), 20)]),
				)
			}
			// Continue without setting user context
			// The @auth directive will handle the authorization check
			ctx.Next()
			return
		}

		// Set both userId and email in context from JWT claims
		wrappedCtx := context.WithValue(ctx.Request.Context(), "userId", claims.UserID.String())
		wrappedCtx = context.WithValue(wrappedCtx, "userEmail", claims.Email)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}

// Helper function for min (Go 1.21+ has this built-in)
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
