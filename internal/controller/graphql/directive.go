package graphql

import (
	"context"

	"github.com/99designs/gqlgen/graphql"
	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

func AuthDirective(ctx context.Context, obj interface{}, next graphql.Resolver) (interface{}, error) {
	// Get user ID from context (set by JWT middleware)
	userIdStr := ctx.Value("userId")
	if userIdStr == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	// Validate that the user ID is a valid UUID
	userIdString, ok := userIdStr.(string)
	if !ok || userIdString == "" {
		return nil, utils.ErrAccessTokenInvalid
	}

	// Additional validation: ensure it's a valid UUID format
	if _, err := uuid.Parse(userIdString); err != nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	return next(ctx)
}
