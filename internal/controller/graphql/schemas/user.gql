type User {
  id: ID!
  email: String
  isFirstLogin: Boolean!
  isExportedWallet: Boolean!
  invitationCode: String
  createdAt: Time!
  updatedAt: Time!
  wallets: [UserWallet!]!
  referral: Referral
  referralSnapshot: ReferralSnapshot
  referrals: [Referral!]!
}

type UserWallet {
  id: ID!
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType
  createdAt: Time!
  updatedAt: Time!
  user: User!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: Time!
  user: User!
  referrer: User
}

type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  user: User!
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserInvitationCodeInput {
  invitationCode: String!
  email: String
  isFirstLogin: Boolean
  isExportedWallet: Boolean
  chain: String
  name: String
  walletAddress: String
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

type CreateUserResponse {
  user: User!
  #token: String!
  success: Boolean!
  message: String!
}

input CreateUserWithReferralInput {
  invitationCode: String!
}

type Query {
  # Get user by ID
  user: User @auth

  # Get user wallets
  userWallets: [UserWallet!]! @auth

  # Get referral information
  referralInfo: Referral @auth

  # Get referral snapshot
  referralSnapshot: ReferralSnapshot @auth
}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Update user first login status
  updateFirstLoginStatus: User! @auth

  # Update wallet export status
  updateWalletExportStatus: User! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth
}
